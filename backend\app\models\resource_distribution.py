"""
资源分布数据模型
"""
from sqlalchemy import Column, String, Text, Integer, JSON, Float, Foreign<PERSON>ey, <PERSON><PERSON><PERSON>, Enum as SQLEnum
from sqlalchemy.orm import relationship
from typing import Dict, Any, List
from enum import Enum

from .base import ProjectBaseModel, TaggedMixin, VersionedMixin


class ResourceType(str, Enum):
    """资源类型枚举"""
    MINERAL = "mineral"             # 矿物
    PLANT = "plant"                 # 植物
    ANIMAL = "animal"               # 动物
    MAGICAL = "magical"             # 魔法
    ENERGY = "energy"               # 能量
    WATER = "water"                 # 水源
    FOOD = "food"                   # 食物
    MATERIAL = "material"           # 材料
    ARTIFACT = "artifact"           # 文物
    KNOWLEDGE = "knowledge"         # 知识
    SPIRITUAL = "spiritual"         # 精神
    OTHER = "other"                 # 其他


class ResourceRarity(str, Enum):
    """资源稀有度枚举"""
    ABUNDANT = "abundant"           # 丰富
    COMMON = "common"               # 常见
    UNCOMMON = "uncommon"           # 不常见
    RARE = "rare"                   # 稀有
    VERY_RARE = "very_rare"         # 极稀有
    LEGENDARY = "legendary"         # 传说
    UNIQUE = "unique"               # 唯一
    UNKNOWN = "unknown"             # 未知


class ExtractionDifficulty(str, Enum):
    """开采难度枚举"""
    TRIVIAL = "trivial"             # 微不足道
    EASY = "easy"                   # 简单
    MODERATE = "moderate"           # 中等
    HARD = "hard"                   # 困难
    VERY_HARD = "very_hard"         # 极困难
    IMPOSSIBLE = "impossible"       # 不可能
    UNKNOWN = "unknown"             # 未知


class ResourceDistribution(ProjectBaseModel, TaggedMixin, VersionedMixin):
    """资源分布模型"""

    __tablename__ = "resource_distributions"

    # 基本信息
    resource_type = Column(SQLEnum(ResourceType), default=ResourceType.MINERAL, comment="资源类型")
    resource_rarity = Column(SQLEnum(ResourceRarity), default=ResourceRarity.COMMON, comment="资源稀有度")
    extraction_difficulty = Column(SQLEnum(ExtractionDifficulty), default=ExtractionDifficulty.MODERATE, comment="开采难度")
    
    # 资源属性
    resource_name = Column(String(200), comment="资源名称")
    resource_properties = Column(JSON, comment="资源属性")
    quality_grades = Column(JSON, comment="品质等级")
    uses_applications = Column(JSON, comment="用途应用")
    
    # 分布信息
    distribution_pattern = Column(String(100), comment="分布模式")
    concentration_areas = Column(JSON, comment="集中区域")
    scattered_locations = Column(JSON, comment="散布地点")
    seasonal_availability = Column(JSON, comment="季节性可用性")
    
    # 地理分布
    geographic_regions = Column(JSON, comment="地理区域")
    terrain_preferences = Column(JSON, comment="地形偏好")
    climate_requirements = Column(JSON, comment="气候要求")
    elevation_range = Column(JSON, comment="海拔范围")
    
    # 数量信息
    total_reserves = Column(Float, comment="总储量")
    annual_yield = Column(Float, comment="年产量")
    depletion_rate = Column(Float, comment="枯竭率")
    regeneration_rate = Column(Float, comment="再生率")
    
    # 开采信息
    extraction_methods = Column(JSON, comment="开采方法")
    required_tools = Column(JSON, comment="所需工具")
    required_skills = Column(JSON, comment="所需技能")
    extraction_risks = Column(JSON, comment="开采风险")
    
    # 经济价值
    market_value = Column(Float, comment="市场价值")
    trade_routes = Column(JSON, comment="贸易路线")
    major_traders = Column(JSON, comment="主要商人")
    price_fluctuations = Column(JSON, comment="价格波动")
    
    # 控制与所有权
    controlling_factions = Column(JSON, comment="控制势力")
    ownership_disputes = Column(JSON, comment="所有权争议")
    access_restrictions = Column(JSON, comment="访问限制")
    mining_rights = Column(JSON, comment="开采权")
    
    # 环境影响
    environmental_impact = Column(JSON, comment="环境影响")
    ecosystem_effects = Column(JSON, comment="生态系统影响")
    pollution_risks = Column(JSON, comment="污染风险")
    conservation_efforts = Column(JSON, comment="保护措施")
    
    # 历史与传说
    discovery_history = Column(Text, comment="发现历史")
    historical_significance = Column(Text, comment="历史意义")
    legends_myths = Column(JSON, comment="传说神话")
    famous_deposits = Column(JSON, comment="著名矿藏")
    
    # 研究与探索
    research_status = Column(String(100), comment="研究状态")
    exploration_progress = Column(Float, default=0.0, comment="探索进度")
    undiscovered_potential = Column(JSON, comment="未发现潜力")
    scientific_studies = Column(JSON, comment="科学研究")
    
    # 关联关系
    project_id = Column(Integer, ForeignKey("projects.id"), comment="项目ID")
    world_setting_id = Column(Integer, ForeignKey("world_settings.id"), comment="世界设定ID")
    map_structure_id = Column(Integer, ForeignKey("map_structures.id"), comment="地图结构ID")

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self._init_default_data()

    def _init_default_data(self):
        """初始化默认数据"""
        if not self.resource_properties:
            self.resource_properties = {}
        if not self.quality_grades:
            self.quality_grades = []
        if not self.uses_applications:
            self.uses_applications = []
        if not self.concentration_areas:
            self.concentration_areas = []
        if not self.scattered_locations:
            self.scattered_locations = []
        if not self.seasonal_availability:
            self.seasonal_availability = {}
        if not self.geographic_regions:
            self.geographic_regions = []
        if not self.terrain_preferences:
            self.terrain_preferences = []
        if not self.climate_requirements:
            self.climate_requirements = {}
        if not self.elevation_range:
            self.elevation_range = {}
        if not self.extraction_methods:
            self.extraction_methods = []
        if not self.required_tools:
            self.required_tools = []
        if not self.required_skills:
            self.required_skills = []
        if not self.extraction_risks:
            self.extraction_risks = []
        if not self.trade_routes:
            self.trade_routes = []
        if not self.major_traders:
            self.major_traders = []
        if not self.price_fluctuations:
            self.price_fluctuations = []
        if not self.controlling_factions:
            self.controlling_factions = []
        if not self.ownership_disputes:
            self.ownership_disputes = []
        if not self.access_restrictions:
            self.access_restrictions = []
        if not self.mining_rights:
            self.mining_rights = []
        if not self.environmental_impact:
            self.environmental_impact = {}
        if not self.ecosystem_effects:
            self.ecosystem_effects = []
        if not self.pollution_risks:
            self.pollution_risks = []
        if not self.conservation_efforts:
            self.conservation_efforts = []
        if not self.legends_myths:
            self.legends_myths = []
        if not self.famous_deposits:
            self.famous_deposits = []
        if not self.undiscovered_potential:
            self.undiscovered_potential = {}
        if not self.scientific_studies:
            self.scientific_studies = []

    def add_concentration_area(self, area_data: Dict[str, Any]):
        """添加集中区域"""
        self.concentration_areas.append(area_data)

    def add_extraction_method(self, method_data: Dict[str, Any]):
        """添加开采方法"""
        self.extraction_methods.append(method_data)

    def add_trade_route(self, route_data: Dict[str, Any]):
        """添加贸易路线"""
        self.trade_routes.append(route_data)

    def add_controlling_faction(self, faction_data: Dict[str, Any]):
        """添加控制势力"""
        self.controlling_factions.append(faction_data)

    def add_famous_deposit(self, deposit_data: Dict[str, Any]):
        """添加著名矿藏"""
        self.famous_deposits.append(deposit_data)

    def calculate_economic_value(self) -> float:
        """计算经济价值"""
        value = self.market_value or 0.0
        
        # 稀有度加成
        rarity_multiplier = {
            ResourceRarity.ABUNDANT: 0.5,
            ResourceRarity.COMMON: 1.0,
            ResourceRarity.UNCOMMON: 1.5,
            ResourceRarity.RARE: 2.5,
            ResourceRarity.VERY_RARE: 4.0,
            ResourceRarity.LEGENDARY: 6.0,
            ResourceRarity.UNIQUE: 10.0,
            ResourceRarity.UNKNOWN: 1.0
        }
        value *= rarity_multiplier.get(self.resource_rarity, 1.0)
        
        # 开采难度影响
        difficulty_modifier = {
            ExtractionDifficulty.TRIVIAL: 0.8,
            ExtractionDifficulty.EASY: 0.9,
            ExtractionDifficulty.MODERATE: 1.0,
            ExtractionDifficulty.HARD: 1.2,
            ExtractionDifficulty.VERY_HARD: 1.5,
            ExtractionDifficulty.IMPOSSIBLE: 0.0,
            ExtractionDifficulty.UNKNOWN: 1.0
        }
        value *= difficulty_modifier.get(self.extraction_difficulty, 1.0)
        
        return value

    def calculate_accessibility(self) -> float:
        """计算可达性"""
        accessibility = 50.0  # 基础分数
        
        # 开采难度影响
        difficulty_penalty = {
            ExtractionDifficulty.TRIVIAL: 30,
            ExtractionDifficulty.EASY: 20,
            ExtractionDifficulty.MODERATE: 0,
            ExtractionDifficulty.HARD: -15,
            ExtractionDifficulty.VERY_HARD: -30,
            ExtractionDifficulty.IMPOSSIBLE: -50,
            ExtractionDifficulty.UNKNOWN: -10
        }
        accessibility += difficulty_penalty.get(self.extraction_difficulty, 0)
        
        # 贸易路线加成
        accessibility += len(self.trade_routes) * 5
        
        # 访问限制影响
        accessibility -= len(self.access_restrictions) * 3
        
        # 控制势力影响
        if len(self.controlling_factions) == 1:
            accessibility += 10  # 单一控制更稳定
        elif len(self.controlling_factions) > 1:
            accessibility -= 5   # 多方控制可能有冲突
        
        return min(100.0, max(0.0, accessibility))

    def calculate_sustainability(self) -> float:
        """计算可持续性"""
        sustainability = 50.0  # 基础分数
        
        # 再生率影响
        if self.regeneration_rate:
            if self.regeneration_rate > self.depletion_rate:
                sustainability += 30
            elif self.regeneration_rate == self.depletion_rate:
                sustainability += 10
            else:
                sustainability -= 20
        
        # 环境保护措施
        sustainability += len(self.conservation_efforts) * 5
        
        # 污染风险影响
        sustainability -= len(self.pollution_risks) * 3
        
        # 总储量影响
        if self.total_reserves:
            if self.total_reserves > 1000000:
                sustainability += 15
            elif self.total_reserves > 100000:
                sustainability += 10
            elif self.total_reserves < 1000:
                sustainability -= 15
        
        return min(100.0, max(0.0, sustainability))

    def validate_consistency(self) -> List[str]:
        """验证资源分布一致性"""
        issues = []
        
        # 检查数量的合理性
        if self.total_reserves and self.total_reserves < 0:
            issues.append("总储量不能为负数")
        
        if self.annual_yield and self.annual_yield < 0:
            issues.append("年产量不能为负数")
        
        # 检查再生率与枯竭率
        if self.regeneration_rate and self.depletion_rate:
            if self.regeneration_rate < 0 or self.depletion_rate < 0:
                issues.append("再生率和枯竭率不能为负数")
        
        # 检查稀有度与分布的一致性
        if self.resource_rarity == ResourceRarity.UNIQUE and len(self.concentration_areas) > 1:
            issues.append("唯一资源不应有多个集中区域")
        
        return issues

    def generate_summary(self) -> str:
        """生成资源分布摘要"""
        summary_parts = []
        
        if self.resource_name:
            summary_parts.append(self.resource_name)
        elif self.description:
            summary_parts.append(self.description)
        
        # 资源类型
        type_map = {
            ResourceType.MINERAL: "矿物",
            ResourceType.PLANT: "植物",
            ResourceType.ANIMAL: "动物",
            ResourceType.MAGICAL: "魔法",
            ResourceType.ENERGY: "能量",
            ResourceType.WATER: "水源",
            ResourceType.FOOD: "食物",
            ResourceType.MATERIAL: "材料",
            ResourceType.ARTIFACT: "文物",
            ResourceType.KNOWLEDGE: "知识",
            ResourceType.SPIRITUAL: "精神",
            ResourceType.OTHER: "其他"
        }
        summary_parts.append(f"类型: {type_map.get(self.resource_type, '未知')}")
        
        # 稀有度
        rarity_map = {
            ResourceRarity.ABUNDANT: "丰富",
            ResourceRarity.COMMON: "常见",
            ResourceRarity.UNCOMMON: "不常见",
            ResourceRarity.RARE: "稀有",
            ResourceRarity.VERY_RARE: "极稀有",
            ResourceRarity.LEGENDARY: "传说",
            ResourceRarity.UNIQUE: "唯一",
            ResourceRarity.UNKNOWN: "未知"
        }
        summary_parts.append(f"稀有度: {rarity_map.get(self.resource_rarity, '未知')}")
        
        # 集中区域数量
        summary_parts.append(f"分布区域: {len(self.concentration_areas)}个")
        
        # 经济价值
        economic_value = self.calculate_economic_value()
        summary_parts.append(f"经济价值: {economic_value:.1f}")
        
        return " | ".join(summary_parts)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        result = super().to_dict()
        result["summary"] = self.generate_summary()
        result["economic_value"] = self.calculate_economic_value()
        result["accessibility"] = self.calculate_accessibility()
        result["sustainability"] = self.calculate_sustainability()
        result["consistency_issues"] = self.validate_consistency()
        result["tags"] = self.get_tags()
        return result
